-- Restore the original lock_pending_tasks function with the simple future OSL logic
-- This reverts back to the working state before the disc_checks_completed status

CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
RETURNS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                status = 'pending'
                AND (
                    -- Normal tasks: scheduled for now or in the past
                    scheduled_at <= task_time
                    OR
                    -- Special case: future publish_product_osl tasks for secondary action
                    (task_type = 'publish_product_osl' AND scheduled_at > task_time)
                )
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY 
                -- Prioritize tasks that are ready to run, then future OSL tasks
                CASE 
                    WHEN scheduled_at <= task_time THEN 0 
                    ELSE 1 
                END,
                scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Restored original lock_pending_tasks function.';
END $$;
